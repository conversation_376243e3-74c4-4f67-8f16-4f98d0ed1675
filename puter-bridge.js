/**
 * Node.js Bridge Server for Puter.js Claude API
 * 
 * This server runs alongside the PHP server and handles the actual
 * communication with Puter.js Claude API since PHP cannot directly
 * execute browser JavaScript.
 */

const express = require('express');
const cors = require('cors');
const puppeteer = require('puppeteer');

const app = express();
const PORT = process.env.PUTER_BRIDGE_PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Global variables
let browser = null;
let page = null;
let isInitialized = false;

/**
 * Initialize Puter.js in a headless browser
 */
async function initializePuter() {
    try {
        console.log('Initializing Puter.js with headless browser...');

        // Launch headless browser
        browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        page = await browser.newPage();

        // Set up console logging from the browser
        page.on('console', msg => {
            console.log('Browser:', msg.text());
        });

        page.on('pageerror', error => {
            console.error('Browser error:', error.message);
        });

        // Create HTML page with Puter.js
        const html = `
            <!DOCTYPE html>
            <html>
            <head>
                <script src="https://js.puter.com/v2/"></script>
            </head>
            <body>
                <div id="status">Loading Puter.js...</div>
                <script>
                    window.puterReady = false;

                    // Wait for Puter.js to load
                    const checkPuter = () => {
                        if (typeof puter !== 'undefined') {
                            console.log('Puter.js loaded successfully');
                            window.puterReady = true;
                            document.getElementById('status').textContent = 'Puter.js ready';
                        } else {
                            setTimeout(checkPuter, 100);
                        }
                    };

                    setTimeout(checkPuter, 1000);
                </script>
            </body>
            </html>
        `;

        await page.setContent(html);

        // Wait for Puter.js to be ready
        await page.waitForFunction(() => window.puterReady, { timeout: 30000 });

        console.log('Puter.js initialized successfully');
        isInitialized = true;

    } catch (error) {
        console.error('Failed to initialize Puter.js:', error);
        if (browser) {
            await browser.close();
        }
        throw error;
    }
}

/**
 * Estimate token count (rough approximation)
 */
function estimateTokens(text) {
    if (!text) return 0;
    return Math.ceil(text.length / 4);
}

/**
 * Generate OpenAI-compatible response format
 */
function formatOpenAIResponse(content, model, completionId, isStreaming = false) {
    const timestamp = Math.floor(Date.now() / 1000);
    
    if (isStreaming) {
        return {
            id: completionId,
            object: 'chat.completion.chunk',
            created: timestamp,
            model: model,
            choices: [{
                index: 0,
                delta: {
                    content: content
                },
                finish_reason: null
            }],
            usage: null
        };
    } else {
        return {
            id: completionId,
            object: 'chat.completion',
            created: timestamp,
            model: model,
            choices: [{
                index: 0,
                message: {
                    role: 'assistant',
                    content: content
                },
                finish_reason: 'stop'
            }],
            usage: {
                prompt_tokens: estimateTokens(''), // Will be calculated by caller
                completion_tokens: estimateTokens(content),
                total_tokens: estimateTokens(content)
            }
        };
    }
}

/**
 * Health check endpoint
 */
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        initialized: isInitialized,
        timestamp: new Date().toISOString()
    });
});

/**
 * Non-streaming chat completion
 */
app.post('/chat/completions', async (req, res) => {
    try {
        if (!isInitialized || !page) {
            return res.status(503).json({
                error: {
                    message: 'Puter client not initialized',
                    type: 'service_unavailable'
                }
            });
        }

        const { prompt, model = 'claude-sonnet-4', completion_id } = req.body;

        if (!prompt) {
            return res.status(400).json({
                error: {
                    message: 'Prompt is required',
                    type: 'invalid_request_error'
                }
            });
        }

        console.log(`Processing non-streaming request: ${model}`);

        // Call Puter.js Claude API through the browser page
        const result = await page.evaluate(async (prompt, model) => {
            try {
                const response = await puter.ai.chat(prompt, { model: model });
                return {
                    success: true,
                    content: response.message.content[0].text
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }, prompt, model);

        if (!result.success) {
            throw new Error(result.error);
        }

        // Format as OpenAI response
        const openaiResponse = formatOpenAIResponse(result.content, model, completion_id);
        openaiResponse.usage.prompt_tokens = estimateTokens(prompt);
        openaiResponse.usage.total_tokens = openaiResponse.usage.prompt_tokens + openaiResponse.usage.completion_tokens;

        res.json(openaiResponse);

    } catch (error) {
        console.error('Error in non-streaming completion:', error);
        res.status(500).json({
            error: {
                message: error.message || 'Internal server error',
                type: 'server_error'
            }
        });
    }
});

/**
 * Streaming chat completion
 */
app.post('/chat/completions/stream', async (req, res) => {
    try {
        if (!isInitialized || !puterClient) {
            return res.status(503).json({
                error: {
                    message: 'Puter client not initialized',
                    type: 'service_unavailable'
                }
            });
        }
        
        const { prompt, model = 'claude-sonnet-4', completion_id } = req.body;
        
        if (!prompt) {
            return res.status(400).json({
                error: {
                    message: 'Prompt is required',
                    type: 'invalid_request_error'
                }
            });
        }
        
        console.log(`Processing streaming request: ${model}`);
        
        // Set up Server-Sent Events
        res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        });
        
        try {
            // Send initial chunk with role
            const initialChunk = formatOpenAIResponse('', model, completion_id, true);
            initialChunk.choices[0].delta = { role: 'assistant', content: '' };
            res.write(`data: ${JSON.stringify(initialChunk)}\n\n`);
            
            // Call Puter.js streaming API
            const response = await puterClient.ai.chat(prompt, { 
                model: model, 
                stream: true 
            });
            
            let fullContent = '';
            
            for await (const part of response) {
                if (part?.text) {
                    fullContent += part.text;
                    
                    // Send chunk in OpenAI format
                    const chunk = formatOpenAIResponse(part.text, model, completion_id, true);
                    res.write(`data: ${JSON.stringify(chunk)}\n\n`);
                }
            }
            
            // Send final chunk with finish_reason
            const finalChunk = formatOpenAIResponse('', model, completion_id, true);
            finalChunk.choices[0].delta = {};
            finalChunk.choices[0].finish_reason = 'stop';
            res.write(`data: ${JSON.stringify(finalChunk)}\n\n`);
            
            // Send usage information chunk
            const usageChunk = {
                id: completion_id,
                object: 'chat.completion.chunk',
                created: Math.floor(Date.now() / 1000),
                model: model,
                choices: [],
                usage: {
                    prompt_tokens: estimateTokens(prompt),
                    completion_tokens: estimateTokens(fullContent),
                    total_tokens: estimateTokens(prompt) + estimateTokens(fullContent)
                }
            };
            res.write(`data: ${JSON.stringify(usageChunk)}\n\n`);
            
            // Send done signal
            res.write('data: [DONE]\n\n');
            
        } catch (streamError) {
            console.error('Streaming error:', streamError);
            const errorChunk = {
                error: {
                    message: streamError.message || 'Streaming error',
                    type: 'server_error'
                }
            };
            res.write(`data: ${JSON.stringify(errorChunk)}\n\n`);
        }
        
        res.end();
        
    } catch (error) {
        console.error('Error in streaming completion:', error);
        if (!res.headersSent) {
            res.status(500).json({
                error: {
                    message: error.message || 'Internal server error',
                    type: 'server_error'
                }
            });
        }
    }
});

/**
 * Start the server
 */
async function startServer() {
    try {
        // Initialize Puter.js first
        await initializePuter();
        
        app.listen(PORT, () => {
            console.log(`Puter Bridge Server running on port ${PORT}`);
            console.log(`Health check: http://localhost:${PORT}/health`);
        });
        
    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\nShutting down Puter Bridge Server...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\nShutting down Puter Bridge Server...');
    process.exit(0);
});

// Start the server
startServer();
