var E=Object.defineProperty;var m=Object.getOwnPropertyDescriptor;var O=Object.getOwnPropertyNames;var x=Object.prototype.hasOwnProperty;var y=(s,e)=>{for(var r in e)E(s,r,{get:e[r],enumerable:!0})},w=(s,e,r,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of O(e))!x.call(s,n)&&n!==r&&E(s,n,{get:()=>e[n],enumerable:!(i=m(e,n))||i.enumerable});return s};var $=s=>w(E({},"__esModule",{value:!0}),s);var P={};y(P,{generateCSS:()=>S.generate,parseSelector:()=>d,preprocess:()=>u,unescapeSelector:()=>D,walkAST:()=>_});module.exports=$(P);var g=require("css-tree"),t=require("./constant.js"),S=require("css-tree");const D=(s="")=>{if(typeof s=="string"&&s.indexOf("\\",0)>=0){const e=s.split("\\"),r=e.length;for(let i=1;i<r;i++){let n=e[i];if(n===""&&i===r-1)n=t.U_FFFD;else{const a=/^([\da-f]{1,6}\s?)/i.exec(n);if(a){const[,o]=a;let l;try{const c=parseInt("D800",t.HEX),p=parseInt("DFFF",t.HEX),h=parseInt(o,t.HEX);h===0||h>=c&&h<=p?l=t.U_FFFD:l=String.fromCodePoint(h)}catch{l=t.U_FFFD}let f="";n.length>o.length&&(f=n.substring(o.length)),n=`${l}${f}`}else/^[\n\r\f]/.test(n)&&(n="\\"+n)}e[i]=n}s=e.join("")}return s},u=(...s)=>{if(!s.length)throw new TypeError("1 argument required, but only 0 present.");let[e]=s;if(typeof e=="string"){let r=0;for(;r>=0&&(r=e.indexOf("#",r),!(r<0));){const i=e.substring(0,r+1);let n=e.substring(r+1);const a=n.codePointAt(0);if(a===t.BIT_HYPHEN){if(/^\d$/.test(n.substring(1,2)))throw new DOMException(`Invalid selector ${e}`,t.SYNTAX_ERR)}else if(a>t.MAX_BIT_16){const o=`\\${a.toString(t.HEX)} `;n.length===t.DUO?n=o:n=`${o}${n.substring(t.DUO)}`}e=`${i}${n}`,r++}e=e.replace(/\f|\r\n?/g,`
`).replace(/[\0\uD800-\uDFFF]|\\$/g,t.U_FFFD)}else if(e==null)e=Object.prototype.toString.call(e).slice(t.TYPE_FROM,t.TYPE_TO).toLowerCase();else if(Array.isArray(e))e=e.join(",");else if(Object.prototype.hasOwnProperty.call(e,"toString"))e=e.toString();else throw new DOMException(`Invalid selector ${e}`,t.SYNTAX_ERR);return e},d=s=>{if(s=u(s),/^$|^\s*>|,\s*$/.test(s))throw new DOMException(`Invalid selector ${s}`,t.SYNTAX_ERR);let e;try{const r=(0,g.parse)(s,{context:"selectorList",parseCustomProperty:!0});e=(0,g.toPlainObject)(r)}catch(r){const i=/(:lang\(\s*("[A-Za-z\d\-*]+")\s*\))/;if(r.message==="Identifier is expected"&&i.test(s)){const[,n,a]=i.exec(s),o=a.replaceAll("*","\\*").replace(/^"/,"").replace(/"$/,""),l=n.replace(a,o);e=d(s.replace(n,l))}else if(r.message==='"]" is expected'&&!s.endsWith("]"))e=d(`${s}]`);else if(r.message==='")" is expected'&&!s.endsWith(")"))e=d(`${s})`);else throw new DOMException(r.message,t.SYNTAX_ERR)}return e},_=(s={})=>{const e=new Set;let r;return(0,g.walk)(s,{enter:n=>{n.type===t.SELECTOR?e.add(n.children):(n.type===t.SELECTOR_PSEUDO_CLASS&&t.REG_LOGICAL_PSEUDO.test(n.name)||n.type===t.SELECTOR_PSEUDO_ELEMENT&&t.REG_SHADOW_PSEUDO.test(n.name))&&(r=!0)}}),r&&(0,g.findAll)(s,(n,a,o)=>{if(o){if(n.type===t.SELECTOR_PSEUDO_CLASS&&t.REG_LOGICAL_PSEUDO.test(n.name)){const l=o.filter(f=>{const{name:c,type:p}=f;return p===t.SELECTOR_PSEUDO_CLASS&&t.REG_LOGICAL_PSEUDO.test(c)});for(const{children:f}of l)for(const{children:c}of f)for(const{children:p}of c)e.has(p)&&e.delete(p)}else if(n.type===t.SELECTOR_PSEUDO_ELEMENT&&t.REG_SHADOW_PSEUDO.test(n.name)){const l=o.filter(f=>{const{name:c,type:p}=f;return p===t.SELECTOR_PSEUDO_ELEMENT&&t.REG_SHADOW_PSEUDO.test(c)});for(const{children:f}of l)for(const{children:c}of f)e.has(c)&&e.delete(c)}}}),[...e]};0&&(module.exports={generateCSS,parseSelector,preprocess,unescapeSelector,walkAST});
//# sourceMappingURL=parser.js.map
