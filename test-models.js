/**
 * Test script to demonstrate Claude Sonnet 4 and Opus 4 model mapping
 */

const axios = require('axios');

const API_URL = 'http://localhost:8000';

// Test different model mappings
const TEST_MODELS = [
    { input: 'claude-sonnet-4', expected: 'claude-sonnet-4', description: 'Direct Claude Sonnet 4' },
    { input: 'claude-opus-4', expected: 'claude-opus-4', description: 'Direct Claude Opus 4' },
    { input: 'gpt-4', expected: 'claude-sonnet-4', description: 'GPT-4 → Claude Sonnet 4' },
    { input: 'gpt-4o', expected: 'claude-opus-4', description: 'GPT-4o → Claude Opus 4' },
    { input: 'gpt-3.5-turbo', expected: 'claude-sonnet-4', description: 'GPT-3.5 → Claude Sonnet 4' }
];

async function testModelMapping() {
    console.log('🧪 Testing Claude Sonnet 4 & Opus 4 Model Mapping\n');
    
    // First check if server is running
    try {
        await axios.get(`${API_URL}/health`);
        console.log('✅ Server is running\n');
    } catch (error) {
        console.error('❌ Server is not running. Please start with: npm start');
        process.exit(1);
    }
    
    // Test each model mapping
    for (const test of TEST_MODELS) {
        console.log(`🔍 Testing: ${test.description}`);
        console.log(`   Input model: ${test.input}`);
        console.log(`   Expected: ${test.expected}`);
        
        try {
            const response = await axios.post(`${API_URL}/v1/chat/completions`, {
                model: test.input,
                messages: [
                    { role: 'user', content: `Say exactly: "Hello from ${test.expected}!" and nothing else.` }
                ]
            }, {
                headers: { 
                    'Authorization': 'Bearer dummy-key',
                    'Content-Type': 'application/json'
                },
                timeout: 30000 // 30 second timeout
            });
            
            console.log(`   ✅ Response model: ${response.data.model}`);
            console.log(`   📝 Content: ${response.data.choices[0].message.content}`);
            console.log(`   🔢 Tokens: ${response.data.usage.total_tokens}\n`);
            
        } catch (error) {
            if (error.code === 'ECONNABORTED') {
                console.log(`   ⏰ Timeout - this is expected if browser interface isn't ready`);
            } else {
                console.log(`   ❌ Error: ${error.message}`);
            }
            console.log('');
        }
    }
    
    console.log('📋 Model Mapping Summary:');
    console.log('========================');
    TEST_MODELS.forEach(test => {
        console.log(`${test.input.padEnd(15)} → ${test.expected}`);
    });
    
    console.log('\n💡 To complete the test:');
    console.log('1. Open http://localhost:8000 in your browser');
    console.log('2. Wait for "Puter.js ready!" message');
    console.log('3. Run this test again');
    
    console.log('\n🎯 For Cursor/Windsurf:');
    console.log('• Use "claude-sonnet-4" for fast, high-quality responses');
    console.log('• Use "claude-opus-4" for the most capable model');
    console.log('• Use "gpt-4o" which maps to claude-opus-4');
    console.log('• Use "gpt-4" which maps to claude-sonnet-4');
}

async function testStreamingModel() {
    console.log('\n🌊 Testing Streaming with Claude Sonnet 4...');
    
    try {
        const response = await axios.post(`${API_URL}/v1/chat/completions`, {
            model: 'claude-sonnet-4',
            messages: [
                { role: 'user', content: 'Count from 1 to 5, one number per response chunk.' }
            ],
            stream: true
        }, {
            headers: { 
                'Authorization': 'Bearer dummy-key',
                'Content-Type': 'application/json'
            },
            responseType: 'stream',
            timeout: 30000
        });
        
        console.log('✅ Streaming started...');
        
        let chunkCount = 0;
        let content = '';
        
        response.data.on('data', (chunk) => {
            const lines = chunk.toString().split('\n');
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);
                    
                    if (data === '[DONE]') {
                        console.log('✅ Streaming completed');
                        console.log(`   Total chunks: ${chunkCount}`);
                        console.log(`   Full content: "${content}"`);
                        return;
                    }
                    
                    try {
                        const parsed = JSON.parse(data);
                        if (parsed.choices?.[0]?.delta?.content) {
                            const deltaContent = parsed.choices[0].delta.content;
                            content += deltaContent;
                            chunkCount++;
                            console.log(`   Chunk ${chunkCount}: "${deltaContent}"`);
                        }
                    } catch (e) {
                        // Ignore JSON parse errors
                    }
                }
            }
        });
        
        response.data.on('error', (error) => {
            console.error('❌ Streaming error:', error.message);
        });
        
    } catch (error) {
        if (error.code === 'ECONNABORTED') {
            console.log('⏰ Streaming timeout - browser interface may not be ready');
        } else {
            console.log('❌ Streaming test failed:', error.message);
        }
    }
}

async function runAllTests() {
    await testModelMapping();
    await testStreamingModel();
}

if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = { testModelMapping, testStreamingModel };
