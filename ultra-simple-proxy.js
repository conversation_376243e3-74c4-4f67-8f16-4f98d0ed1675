/**
 * Ultra Simple OpenAI-Compatible Puter Proxy
 * 
 * This creates a simple server that serves a web page where Puter.js runs,
 * and provides OpenAI-compatible API endpoints that communicate with that page.
 */

const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 8000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Store for managing requests/responses between API and browser
const pendingRequests = new Map();
let requestId = 0;

// Model mapping
const MODEL_MAPPING = {
    'gpt-4': 'claude-opus-4',
    'gpt-4-turbo': 'claude-opus-4',
    'gpt-4o': 'claude-opus-4',
    'gpt-4o-mini': 'claude-sonnet-4',
    'gpt-3.5-turbo': 'claude-sonnet-4',
    'claude-3-opus': 'claude-opus-4',
    'claude-3-sonnet': 'claude-sonnet-4',
    'claude-sonnet-4': 'claude-sonnet-4',
    'claude-opus-4': 'claude-opus-4'
};

/**
 * Generate completion ID
 */
function generateId() {
    return 'chatcmpl-' + Math.random().toString(36).substr(2, 16);
}

/**
 * Convert messages to prompt
 */
function messagesToPrompt(messages) {
    return messages.map(msg => {
        const role = msg.role === 'system' ? 'System' : 
                    msg.role === 'user' ? 'Human' : 'Assistant';
        return `${role}: ${msg.content}`;
    }).join('\n\n');
}

/**
 * Estimate tokens
 */
function estimateTokens(text) {
    return Math.ceil((text || '').length / 4);
}

/**
 * Serve the Puter.js interface page
 */
app.get('/', (req, res) => {
    res.send(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Puter OpenAI Proxy</title>
    <script src="https://js.puter.com/v2/"></script>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .ready { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; border-radius: 5px; max-height: 300px; overflow-y: auto; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
    </style>
</head>
<body>
    <h1>🚀 Puter OpenAI Proxy</h1>
    
    <div id="status" class="status info">Loading Puter.js...</div>
    
    <div class="info">
        <h3>📋 Usage Instructions:</h3>
        <p><strong>API URL:</strong> <code>http://localhost:${PORT}/v1</code></p>
        <p><strong>API Key:</strong> <code>dummy-key</code> (any value works)</p>
        <p><strong>Models:</strong> gpt-4, gpt-3.5-turbo, claude-sonnet-4, etc.</p>
    </div>
    
    <div>
        <button class="btn-primary" onclick="testConnection()">Test Connection</button>
        <button class="btn-success" onclick="clearLog()">Clear Log</button>
    </div>
    
    <div id="log" class="log"></div>

    <script>
        let puterReady = false;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += \`[\${timestamp}] \${message}<br>\`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = \`status \${type}\`;
        }
        
        // Initialize Puter.js
        function initPuter() {
            if (typeof puter !== 'undefined') {
                puterReady = true;
                updateStatus('✅ Puter.js ready! You can now use the API.', 'ready');
                log('Puter.js initialized successfully');
                
                // Listen for API requests
                startListening();
            } else {
                setTimeout(initPuter, 100);
            }
        }
        
        // Start listening for API requests
        function startListening() {
            setInterval(async () => {
                try {
                    const response = await fetch('/api/poll');
                    if (response.ok) {
                        const requests = await response.json();
                        for (const request of requests) {
                            handleRequest(request);
                        }
                    }
                } catch (error) {
                    // Ignore polling errors
                }
            }, 1000);
        }
        
        // Handle API request
        async function handleRequest(request) {
            log(\`Processing request: \${request.model} - \${request.prompt.substring(0, 50)}...\`);
            
            try {
                if (request.stream) {
                    // Handle streaming
                    const response = await puter.ai.chat(request.prompt, { 
                        model: request.puterModel, 
                        stream: true 
                    });
                    
                    let fullContent = '';
                    for await (const part of response) {
                        if (part?.text) {
                            fullContent += part.text;
                            
                            // Send chunk
                            await fetch('/api/response', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({
                                    requestId: request.id,
                                    type: 'chunk',
                                    content: part.text
                                })
                            });
                        }
                    }
                    
                    // Send completion
                    await fetch('/api/response', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            requestId: request.id,
                            type: 'complete',
                            content: fullContent
                        })
                    });
                    
                } else {
                    // Handle non-streaming
                    const response = await puter.ai.chat(request.prompt, { 
                        model: request.puterModel 
                    });
                    
                    const content = response.message.content[0].text;
                    
                    await fetch('/api/response', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            requestId: request.id,
                            type: 'complete',
                            content: content
                        })
                    });
                }
                
                log(\`Request completed: \${request.id}\`);
                
            } catch (error) {
                log(\`Error processing request: \${error.message}\`);
                
                await fetch('/api/response', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        requestId: request.id,
                        type: 'error',
                        error: error.message
                    })
                });
            }
        }
        
        // Test connection
        async function testConnection() {
            if (!puterReady) {
                alert('Puter.js is not ready yet. Please wait.');
                return;
            }
            
            try {
                log('Testing connection...');
                const response = await puter.ai.chat('Say "Hello from Puter!" and nothing else.', { 
                    model: 'claude-sonnet-4' 
                });
                const content = response.message.content[0].text;
                log(\`Test successful: \${content}\`);
                alert(\`Test successful! Response: \${content}\`);
            } catch (error) {
                log(\`Test failed: \${error.message}\`);
                alert(\`Test failed: \${error.message}\`);
            }
        }
        
        // Start initialization
        setTimeout(initPuter, 1000);
    </script>
</body>
</html>
    `);
});

/**
 * Health check
 */
app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

/**
 * Models endpoint
 */
app.get('/v1/models', (req, res) => {
    const models = Object.keys(MODEL_MAPPING).map(id => ({
        id,
        object: 'model',
        created: Math.floor(Date.now() / 1000),
        owned_by: 'puter-proxy'
    }));
    
    res.json({ object: 'list', data: models });
});

/**
 * Polling endpoint for browser to get pending requests
 */
app.get('/api/poll', (req, res) => {
    const requests = Array.from(pendingRequests.values())
        .filter(req => req.status === 'pending')
        .map(req => ({
            id: req.id,
            prompt: req.prompt,
            model: req.model,
            puterModel: req.puterModel,
            originalModel: req.originalModel,
            stream: req.stream,
            completionId: req.completionId,
            status: 'processing'
        }));

    // Mark as processing
    requests.forEach(req => {
        if (pendingRequests.has(req.id)) {
            pendingRequests.get(req.id).status = 'processing';
        }
    });

    res.json(requests);
});

/**
 * Response endpoint for browser to send results
 */
app.post('/api/response', (req, res) => {
    const { requestId, type, content, error } = req.body;
    
    if (pendingRequests.has(requestId)) {
        const request = pendingRequests.get(requestId);
        
        if (type === 'chunk' && request.res && !request.res.headersSent) {
            // Send streaming chunk
            const chunk = {
                id: request.completionId,
                object: 'chat.completion.chunk',
                created: Math.floor(Date.now() / 1000),
                model: request.originalModel,
                choices: [{
                    index: 0,
                    delta: { content },
                    finish_reason: null
                }]
            };
            request.res.write(`data: ${JSON.stringify(chunk)}\n\n`);
            
        } else if (type === 'complete') {
            if (request.stream && request.res && !request.res.headersSent) {
                // Send final streaming chunk
                const finalChunk = {
                    id: request.completionId,
                    object: 'chat.completion.chunk',
                    created: Math.floor(Date.now() / 1000),
                    model: request.originalModel,
                    choices: [{
                        index: 0,
                        delta: {},
                        finish_reason: 'stop'
                    }]
                };
                request.res.write(`data: ${JSON.stringify(finalChunk)}\n\n`);
                request.res.write('data: [DONE]\n\n');
                request.res.end();
            } else if (!request.stream && request.res && !request.res.headersSent) {
                // Send non-streaming response
                const response = {
                    id: request.completionId,
                    object: 'chat.completion',
                    created: Math.floor(Date.now() / 1000),
                    model: request.originalModel,
                    choices: [{
                        index: 0,
                        message: { role: 'assistant', content },
                        finish_reason: 'stop'
                    }],
                    usage: {
                        prompt_tokens: estimateTokens(request.prompt),
                        completion_tokens: estimateTokens(content),
                        total_tokens: estimateTokens(request.prompt) + estimateTokens(content)
                    }
                };
                request.res.json(response);
            }
            
            pendingRequests.delete(requestId);
            
        } else if (type === 'error') {
            if (request.res && !request.res.headersSent) {
                request.res.status(500).json({
                    error: { message: error, type: 'server_error' }
                });
            }
            pendingRequests.delete(requestId);
        }
    }
    
    res.json({ success: true });
});

/**
 * Chat completions endpoint
 */
app.post('/v1/chat/completions', (req, res) => {
    const { messages, model = 'gpt-3.5-turbo', stream = false } = req.body;
    
    if (!messages || !Array.isArray(messages)) {
        return res.status(400).json({
            error: { message: 'Messages are required', type: 'invalid_request_error' }
        });
    }
    
    const puterModel = MODEL_MAPPING[model] || 'claude-sonnet-4';
    const prompt = messagesToPrompt(messages);
    const completionId = generateId();
    const id = ++requestId;
    
    // Store request
    pendingRequests.set(id, {
        id,
        prompt,
        model,
        puterModel,
        originalModel: model,
        stream,
        completionId,
        res,
        status: 'pending',
        timestamp: Date.now()
    });
    
    if (stream) {
        res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive'
        });
        
        // Send initial chunk
        const initialChunk = {
            id: completionId,
            object: 'chat.completion.chunk',
            created: Math.floor(Date.now() / 1000),
            model: model,
            choices: [{
                index: 0,
                delta: { role: 'assistant', content: '' },
                finish_reason: null
            }]
        };
        res.write(`data: ${JSON.stringify(initialChunk)}\n\n`);
    }
    
    // Cleanup old requests
    setTimeout(() => {
        if (pendingRequests.has(id)) {
            const request = pendingRequests.get(id);
            if (request.res && !request.res.headersSent) {
                request.res.status(408).json({
                    error: { message: 'Request timeout', type: 'timeout_error' }
                });
            }
            pendingRequests.delete(id);
        }
    }, 60000); // 1 minute timeout
});

/**
 * Start server
 */
app.listen(PORT, () => {
    console.log(`\n🎉 Ultra Simple Puter OpenAI Proxy running!`);
    console.log(`📍 Open: http://localhost:${PORT}`);
    console.log(`🔗 API: http://localhost:${PORT}/v1`);
    console.log(`\n💡 Instructions:`);
    console.log(`1. Open http://localhost:${PORT} in your browser`);
    console.log(`2. Wait for "Puter.js ready!" message`);
    console.log(`3. Use API URL: http://localhost:${PORT}/v1`);
    console.log(`4. Use any dummy API key`);
});
