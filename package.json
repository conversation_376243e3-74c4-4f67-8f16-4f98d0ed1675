{"name": "openai-puter-proxy", "version": "1.0.0", "description": "OpenAI-compatible API proxy using Puter.js free Claude API", "main": "puter-bridge.js", "scripts": {"start": "node puter-bridge.js", "dev": "nodemon puter-bridge.js", "test": "node test-client.js"}, "keywords": ["openai", "claude", "puter", "api", "proxy", "llm"], "author": "OpenAI-Puter-Proxy", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "jsdom": "^23.0.1", "axios": "^1.6.2", "node-fetch": "^2.7.0", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}