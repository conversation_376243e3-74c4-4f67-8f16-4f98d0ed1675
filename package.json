{"name": "openai-puter-proxy", "version": "1.0.0", "description": "OpenAI-compatible API proxy using Puter.js free Claude API", "main": "ultra-simple-proxy.js", "scripts": {"start": "node ultra-simple-proxy.js", "simple": "node ultra-simple-proxy.js", "dev": "nodemon ultra-simple-proxy.js", "test": "node test-simple.js", "test-models": "node test-models.js"}, "keywords": ["openai", "claude", "puter", "api", "proxy", "llm"], "author": "OpenAI-Puter-Proxy", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}